// -*- mode:C++; tab-width:8; c-basic-offset:2; indent-tabs-mode:t -*-
// vim: ts=8 sw=2 smarttab ft=cpp

/*
 * Ceph - scalable distributed file system
 *
 * Copyright (C) 2024 Red Hat, Inc.
 *
 * This is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License version 2.1, as published by the Free Software
 * Foundation. See file COPYING.
 *
 */

#pragma once

#include <atomic>
#include <chrono>
#include <memory>
#include <mutex>
#include <vector>
#include <algorithm>

#include "common/ceph_time.h"
#include "common/ceph_mutex.h"
#include "include/common_fwd.h"
#include "common/ceph_json.h"

// Forward declarations
namespace rgw { namespace sal {
  class RadosStore;
} }

class DoutPrefixProvider;

namespace rgw {

// Forward declarations
class OSDPerfMonitor;
class TrimThrottleController;
template<typename ItemType> class BatchProcessor;

/**
 * Performance metrics collected from OSD
 */
struct OSDPerfMetrics {
  uint64_t op_queue_depth = 0;      // Current operation queue depth
  uint64_t op_latency_ms = 0;       // Average operation latency in ms
  uint64_t op_throughput = 0;       // Operations per second
  uint64_t cpu_utilization = 0;     // CPU utilization percentage
  uint64_t io_utilization = 0;      // IO utilization percentage
  
  ceph::real_time timestamp;        // When metrics were collected
  
  bool is_overloaded() const {
    return op_queue_depth > 100 || 
           op_latency_ms > 50 ||
           cpu_utilization > 80 ||
           io_utilization > 80;
  }
  
  double load_factor() const {
    // Calculate overall load factor (0.0 to 1.0+)
    double queue_factor = std::min(1.0, op_queue_depth / 100.0);
    double latency_factor = std::min(1.0, op_latency_ms / 50.0);
    double cpu_factor = cpu_utilization / 100.0;
    double io_factor = io_utilization / 100.0;

    return std::max(std::max(queue_factor, latency_factor), std::max(cpu_factor, io_factor));
  }
};

/**
 * Configuration for trim throttling
 */
struct TrimThrottleConfig {
  // Performance thresholds
  uint64_t max_queue_depth = 100;
  uint64_t max_latency_ms = 50;
  uint64_t max_cpu_util = 80;
  uint64_t max_io_util = 80;
  
  // Throttling parameters
  uint32_t min_concurrency = 1;
  uint32_t max_concurrency = 16;
  uint32_t default_concurrency = 8;
  
  // Batch processing
  uint32_t batch_size = 100;
  uint32_t min_batch_size = 10;
  uint32_t max_batch_size = 1000;
  
  // Timing
  std::chrono::milliseconds monitor_interval{5000};  // 5 seconds
  std::chrono::milliseconds batch_delay{100};        // 100ms between batches
  std::chrono::milliseconds adaptive_delay{1000};    // 1 second when overloaded
  
  // Load balancing
  double load_threshold = 0.7;      // Start throttling at 70% load
  double critical_threshold = 0.9;  // Critical load threshold
  
  void validate() {
    min_concurrency = std::max(1U, min_concurrency);
    max_concurrency = std::max(min_concurrency, max_concurrency);
    default_concurrency = std::clamp(default_concurrency, min_concurrency, max_concurrency);
    
    min_batch_size = std::max(1U, min_batch_size);
    max_batch_size = std::max(min_batch_size, max_batch_size);
    batch_size = std::clamp(batch_size, min_batch_size, max_batch_size);
  }
};

/**
 * OSD Performance Monitor
 * Collects real-time performance metrics from OSDs
 */
class OSDPerfMonitor {
public:
  explicit OSDPerfMonitor(CephContext* cct, rgw::sal::RadosStore* store);
  ~OSDPerfMonitor();
  
  // Start/stop monitoring
  int start();
  void stop();
  
  // Get current metrics
  OSDPerfMetrics get_metrics() const;
  
  // Check if system is overloaded
  bool is_overloaded() const;
  
  // Get load factor (0.0 to 1.0+)
  double get_load_factor() const;

private:
  CephContext* cct;
  rgw::sal::RadosStore* store;
  
  mutable ceph::mutex metrics_lock = ceph::make_mutex("OSDPerfMonitor::metrics_lock");
  OSDPerfMetrics current_metrics;
  
  std::atomic<bool> running{false};
  std::unique_ptr<std::thread> monitor_thread;
  
  void monitor_loop();
  int collect_osd_metrics(OSDPerfMetrics& metrics);
  int query_osd_perf_counters(OSDPerfMetrics& metrics);
  int query_cluster_osd_perf(OSDPerfMetrics& metrics);
  int query_individual_osd_perf(OSDPerfMetrics& metrics);
  int parse_osd_perf_json(JSONParser& parser, OSDPerfMetrics& metrics);
  int aggregate_osd_metrics(JSONParser& parser, OSDPerfMetrics& metrics);
};

/**
 * Dynamic Throttle Controller
 * Adjusts trim operation concurrency based on system load
 */
class TrimThrottleController {
public:
  explicit TrimThrottleController(CephContext* cct,
                                  std::shared_ptr<OSDPerfMonitor> monitor,
                                  const TrimThrottleConfig& config = {});
  ~TrimThrottleController();

  // Start/stop controller
  int start();
  void stop();

  // Get current concurrency limit
  uint32_t get_concurrency_limit() const;

  // Get current batch size
  uint32_t get_batch_size() const;

  // Get delay between batches
  std::chrono::milliseconds get_batch_delay() const;

  // Check if should pause trim operations
  bool should_pause() const;

  // Update configuration
  void update_config(const TrimThrottleConfig& new_config);

  // Get current configuration
  TrimThrottleConfig get_config() const;

private:
  CephContext* cct;
  std::shared_ptr<OSDPerfMonitor> perf_monitor;

  mutable ceph::mutex config_lock = ceph::make_mutex("TrimThrottleController::config_lock");
  TrimThrottleConfig config;

  std::atomic<uint32_t> current_concurrency;
  std::atomic<uint32_t> current_batch_size;
  std::atomic<bool> paused{false};

  std::atomic<bool> running{false};
  std::unique_ptr<std::thread> control_thread;

  void control_loop();
  void adjust_parameters(const OSDPerfMetrics& metrics);
  uint32_t calculate_optimal_concurrency(double load_factor) const;
  uint32_t calculate_optimal_batch_size(double load_factor) const;
};

/**
 * Batch Processor
 * Splits large trim operations into manageable batches
 */
template<typename ItemType>
class BatchProcessor {
public:
  using ProcessBatchFunc = std::function<int(const std::vector<ItemType>&)>;

  explicit BatchProcessor(CephContext* cct,
                          std::shared_ptr<TrimThrottleController> controller);

  // Process items in batches with throttling
  int process_items(const std::vector<ItemType>& items,
                    ProcessBatchFunc process_func,
                    const DoutPrefixProvider* dpp);

  // Process items with custom batch size
  int process_items_with_size(const std::vector<ItemType>& items,
                              uint32_t batch_size,
                              ProcessBatchFunc process_func,
                              const DoutPrefixProvider* dpp);

private:
  CephContext* cct;
  std::shared_ptr<TrimThrottleController> throttle_controller;

  int process_single_batch(const std::vector<ItemType>& batch,
                           ProcessBatchFunc process_func,
                           const DoutPrefixProvider* dpp);
};

/**
 * Trim Throttle Manager
 * Main interface for throttled trim operations
 */
class TrimThrottleManager {
public:
  explicit TrimThrottleManager(CephContext* cct, rgw::sal::RadosStore* store);
  ~TrimThrottleManager();

  // Initialize and start all components
  int init(const TrimThrottleConfig& config = {});
  void shutdown();

  // Get components
  std::shared_ptr<OSDPerfMonitor> get_perf_monitor() const { return perf_monitor; }
  std::shared_ptr<TrimThrottleController> get_controller() const { return controller; }

  // Create batch processor for specific item type
  template<typename ItemType>
  std::unique_ptr<BatchProcessor<ItemType>> create_batch_processor() {
    return std::make_unique<BatchProcessor<ItemType>>(cct, controller);
  }

  // Convenience methods
  bool is_overloaded() const;
  uint32_t get_concurrency_limit() const;
  std::chrono::milliseconds get_batch_delay() const;

private:
  CephContext* cct;
  rgw::sal::RadosStore* store;

  std::shared_ptr<OSDPerfMonitor> perf_monitor;
  std::shared_ptr<TrimThrottleController> controller;

  std::atomic<bool> initialized{false};
};

/**
 * Configuration helper for trim throttling
 */
class TrimThrottleConfigHelper {
public:
  static TrimThrottleConfig load_from_ceph_context(CephContext* cct);
  static void update_from_ceph_context(TrimThrottleConfig& config, CephContext* cct);
  static void validate_config(TrimThrottleConfig& config);
};

} // namespace rgw
