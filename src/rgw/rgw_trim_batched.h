// -*- mode:C++; tab-width:8; c-basic-offset:2; indent-tabs-mode:t -*-
// vim: ts=8 sw=2 smarttab ft=cpp

/*
 * Ceph - scalable distributed file system
 *
 * Copyright (C) 2024 Red Hat, Inc.
 *
 * This is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License version 2.1, as published by the Free Software
 * Foundation. See file COPYING.
 *
 */

#pragma once

#include "rgw_coroutine.h"
#include "rgw_trim_throttle.h"

class DoutPrefixProvider;
class RGWHTTPManager;

namespace rgw { namespace sal {
  class RadosStore;
} }

namespace rgw {

/**
 * 分批 Metadata Log Trim 协程
 * 将大批量的 mdlog trim 操作分解为小批次，避免一次性删除过多 key
 */
class BatchedMetaLogTrimCR : public RGWCoroutine {
  rgw::sal::RadosStore* store;
  RGWHTTPManager* http;
  int num_shards;
  uint32_t concurrency_limit;
  uint32_t batch_size;
  std::chrono::milliseconds batch_delay;
  
  // 当前处理状态
  int current_shard = 0;
  std::vector<std::string> pending_entries;
  
public:
  BatchedMetaLogTrimCR(const DoutPrefixProvider* dpp,
                       rgw::sal::RadosStore* store,
                       RGWHTTPManager* http,
                       int num_shards,
                       uint32_t concurrency_limit,
                       uint32_t batch_size,
                       std::chrono::milliseconds batch_delay = std::chrono::milliseconds(100));

  int operate(const DoutPrefixProvider* dpp) override;

private:
  int process_shard_batch(const DoutPrefixProvider* dpp, int shard_id);
  int trim_entries_batch(const DoutPrefixProvider* dpp, int shard_id, 
                         const std::vector<std::string>& entries);
  int collect_trim_entries(const DoutPrefixProvider* dpp, int shard_id,
                           std::vector<std::string>& entries, uint32_t max_entries);
};

/**
 * 分批 Data Log Trim 协程
 * 将大批量的 datalog trim 操作分解为小批次
 */
class BatchedDataLogTrimCR : public RGWCoroutine {
  rgw::sal::RadosStore* store;
  RGWHTTPManager* http;
  int num_shards;
  uint32_t concurrency_limit;
  uint32_t batch_size;
  std::chrono::milliseconds batch_delay;
  
  // 当前处理状态
  int current_shard = 0;
  std::vector<std::string> pending_entries;
  
public:
  BatchedDataLogTrimCR(const DoutPrefixProvider* dpp,
                       rgw::sal::RadosStore* store,
                       RGWHTTPManager* http,
                       int num_shards,
                       uint32_t concurrency_limit,
                       uint32_t batch_size,
                       std::chrono::milliseconds batch_delay = std::chrono::milliseconds(100));

  int operate(const DoutPrefixProvider* dpp) override;

private:
  int process_shard_batch(const DoutPrefixProvider* dpp, int shard_id);
  int trim_entries_batch(const DoutPrefixProvider* dpp, int shard_id,
                         const std::vector<std::string>& entries);
  int collect_trim_entries(const DoutPrefixProvider* dpp, int shard_id,
                           std::vector<std::string>& entries, uint32_t max_entries);
};

/**
 * 分批 Bucket Index Log Trim 协程
 * 将大批量的 bucket index log trim 操作分解为小批次
 */
class BatchedBucketLogTrimCR : public RGWCoroutine {
  rgw::sal::RadosStore* store;
  RGWHTTPManager* http;
  uint32_t concurrency_limit;
  uint32_t batch_size;
  std::chrono::milliseconds batch_delay;
  
  // 当前处理状态
  std::vector<std::string> pending_buckets;
  size_t current_bucket_idx = 0;
  
public:
  BatchedBucketLogTrimCR(const DoutPrefixProvider* dpp,
                         rgw::sal::RadosStore* store,
                         RGWHTTPManager* http,
                         uint32_t concurrency_limit,
                         uint32_t batch_size,
                         std::chrono::milliseconds batch_delay = std::chrono::milliseconds(100));

  int operate(const DoutPrefixProvider* dpp) override;

private:
  int collect_buckets_to_trim(const DoutPrefixProvider* dpp);
  int process_bucket_batch(const DoutPrefixProvider* dpp,
                           const std::string& bucket_instance);
  int trim_bucket_shard(const DoutPrefixProvider* dpp,
                        const RGWBucketInfo& bucket_info,
                        int shard_id,
                        const std::string& bucket_instance);
};

/**
 * 限流的 Shard 收集器
 * 控制并发的 shard 处理，避免同时处理过多 shard
 */
class ThrottledShardProcessor {
public:
  ThrottledShardProcessor(uint32_t max_concurrency, 
                          std::chrono::milliseconds delay_between_batches);
  
  // 处理一批 shard
  template<typename ProcessFunc>
  int process_shards(const DoutPrefixProvider* dpp,
                     const std::vector<int>& shard_ids,
                     ProcessFunc process_func);

private:
  uint32_t max_concurrency;
  std::chrono::milliseconds delay_between_batches;
  
  template<typename ProcessFunc>
  int process_shard_batch(const DoutPrefixProvider* dpp,
                          const std::vector<int>& shard_batch,
                          ProcessFunc process_func);
};

// 工厂函数
RGWCoroutine* create_batched_meta_trim_cr(const DoutPrefixProvider* dpp,
                                          rgw::sal::RadosStore* store,
                                          RGWHTTPManager* http,
                                          int num_shards,
                                          uint32_t concurrency_limit,
                                          uint32_t batch_size);

RGWCoroutine* create_batched_data_trim_cr(const DoutPrefixProvider* dpp,
                                          rgw::sal::RadosStore* store,
                                          RGWHTTPManager* http,
                                          int num_shards,
                                          uint32_t concurrency_limit,
                                          uint32_t batch_size);

RGWCoroutine* create_batched_bucket_trim_cr(const DoutPrefixProvider* dpp,
                                            rgw::sal::RadosStore* store,
                                            RGWHTTPManager* http,
                                            uint32_t concurrency_limit,
                                            uint32_t batch_size);

/**
 * 批量删除辅助函数
 * 提供底层的批量删除操作，避免一次性删除过多对象
 */
class BatchDeleteHelper {
public:
  static int delete_objects_batch(const DoutPrefixProvider* dpp,
                                  rgw::sal::RadosStore* store,
                                  const rgw_pool& pool,
                                  const std::vector<std::string>& object_names,
                                  uint32_t batch_size = 50);
  
  static int delete_log_entries_batch(const DoutPrefixProvider* dpp,
                                      rgw::sal::RadosStore* store,
                                      const std::string& log_pool,
                                      int shard_id,
                                      const std::vector<std::string>& entries,
                                      uint32_t batch_size = 50);

private:
  static int execute_batch_delete(const DoutPrefixProvider* dpp,
                                  librados::IoCtx& ioctx,
                                  const std::vector<std::string>& object_names);
};

} // namespace rgw
