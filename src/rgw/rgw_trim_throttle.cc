// -*- mode:C++; tab-width:8; c-basic-offset:2; indent-tabs-mode:t -*-
// vim: ts=8 sw=2 smarttab ft=cpp

/*
 * Ceph - scalable distributed file system
 *
 * Copyright (C) 2024 Red Hat, Inc.
 *
 * This is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License version 2.1, as published by the Free Software
 * Foundation. See file COPYING.
 *
 */

#include "rgw_trim_throttle.h"

#include <thread>
#include <chrono>

#include "common/dout.h"
#include "common/errno.h"
#include "rgw_sal_rados.h"
#include "rgw_rados.h"
#include "common/ceph_json.h"

#define dout_context g_ceph_context
#define dout_subsys ceph_subsys_rgw
#undef dout_prefix
#define dout_prefix (*_dout << "rgw trim throttle: ")

using namespace std::chrono_literals;

namespace rgw {

// OSDPerfMonitor implementation
OSDPerfMonitor::OSDPerfMonitor(CephContext* cct, rgw::sal::RadosStore* store)
  : cct(cct), store(store) {
}

OSDPerfMonitor::~OSDPerfMonitor() {
  stop();
}

int OSDPerfMonitor::start() {
  if (running.exchange(true)) {
    return 0; // Already running
  }

  dout(10) << __func__ << ": starting OSD performance monitor" << dendl;
  
  try {
    monitor_thread = std::make_unique<std::thread>(&OSDPerfMonitor::monitor_loop, this);
  } catch (const std::exception& e) {
    running = false;
    derr << __func__ << ": failed to start monitor thread: " << e.what() << dendl;
    return -ENOMEM;
  }
  
  return 0;
}

void OSDPerfMonitor::stop() {
  if (!running.exchange(false)) {
    return; // Already stopped
  }

  dout(10) << __func__ << ": stopping OSD performance monitor" << dendl;
  
  if (monitor_thread && monitor_thread->joinable()) {
    monitor_thread->join();
  }
  monitor_thread.reset();
}

OSDPerfMetrics OSDPerfMonitor::get_metrics() const {
  std::lock_guard lock(metrics_lock);
  return current_metrics;
}

bool OSDPerfMonitor::is_overloaded() const {
  return get_metrics().is_overloaded();
}

double OSDPerfMonitor::get_load_factor() const {
  return get_metrics().load_factor();
}

void OSDPerfMonitor::monitor_loop() {
  dout(10) << __func__ << ": monitor loop started" << dendl;
  
  while (running) {
    OSDPerfMetrics metrics;
    metrics.timestamp = ceph::real_clock::now();
    
    int r = collect_osd_metrics(metrics);
    if (r < 0) {
      dout(5) << __func__ << ": failed to collect metrics: " << cpp_strerror(r) << dendl;
    } else {
      std::lock_guard lock(metrics_lock);
      current_metrics = metrics;
      
      dout(20) << __func__ << ": metrics updated - "
               << "queue_depth=" << metrics.op_queue_depth
               << " latency=" << metrics.op_latency_ms << "ms"
               << " throughput=" << metrics.op_throughput
               << " cpu=" << metrics.cpu_utilization << "%"
               << " io=" << metrics.io_utilization << "%"
               << " load_factor=" << metrics.load_factor() << dendl;
    }
    
    // Sleep for monitoring interval
    std::this_thread::sleep_for(5s);
  }
  
  dout(10) << __func__ << ": monitor loop stopped" << dendl;
}

int OSDPerfMonitor::collect_osd_metrics(OSDPerfMetrics& metrics) {
  // Query OSD performance counters
  int r = query_osd_perf_counters(metrics);
  if (r < 0) {
    return r;
  }
  
  return 0;
}

int OSDPerfMonitor::query_osd_perf_counters(OSDPerfMetrics& metrics) {
  try {
    auto rados_store = static_cast<rgw::sal::RadosStore*>(store);
    if (!rados_store) {
      return -EINVAL;
    }

    // Query OSD performance using ceph osd perf command
    int r = query_cluster_osd_perf(metrics);
    if (r < 0) {
      dout(10) << __func__ << ": failed to query cluster OSD perf, using fallback" << dendl;
      // Fallback to individual OSD queries
      r = query_individual_osd_perf(metrics);
    }

    return r;
  } catch (const std::exception& e) {
    derr << __func__ << ": exception: " << e.what() << dendl;
    return -EIO;
  }
}

int OSDPerfMonitor::query_cluster_osd_perf(OSDPerfMetrics& metrics) {
  // Use the cluster's mon command interface to get OSD performance
  auto rados_store = static_cast<rgw::sal::RadosStore*>(store);

  // Query OSD performance summary
  bufferlist inbl, outbl;
  std::string outstring;

  auto rados_svc = rados_store->svc()->rados;
  auto handle = rados_svc->handle();
  int r = handle.mon_command(
    "{\"prefix\": \"osd perf\", \"format\": \"json\"}",
    inbl, &outbl, &outstring);

  if (r < 0) {
    dout(10) << __func__ << ": mon_command osd perf failed: " << cpp_strerror(r) << dendl;
    return r;
  }

  // Parse JSON response
  std::string json_str = outbl.to_str();
  JSONParser parser;

  if (!parser.parse(json_str.c_str(), json_str.length())) {
    dout(5) << __func__ << ": failed to parse JSON response" << dendl;
    return -EINVAL;
  }

  return parse_osd_perf_json(parser, metrics);
}

int OSDPerfMonitor::query_individual_osd_perf(OSDPerfMetrics& metrics) {
  // Query individual OSD statistics and aggregate
  auto rados_store = static_cast<rgw::sal::RadosStore*>(store);

  bufferlist inbl, outbl;
  std::string outstring;

  // Get OSD dump to find active OSDs
  auto rados_svc = rados_store->svc()->rados;
  auto handle = rados_svc->handle();
  int r = handle.mon_command(
    "{\"prefix\": \"osd dump\", \"format\": \"json\"}",
    inbl, &outbl, &outstring);

  if (r < 0) {
    dout(10) << __func__ << ": mon_command osd dump failed: " << cpp_strerror(r) << dendl;
    return r;
  }

  // Parse OSD dump to get list of OSDs
  std::string dump_str = outbl.to_str();
  JSONParser parser;

  if (!parser.parse(dump_str.c_str(), dump_str.length())) {
    dout(5) << __func__ << ": failed to parse OSD dump JSON" << dendl;
    return -EINVAL;
  }

  return aggregate_osd_metrics(parser, metrics);
}

int OSDPerfMonitor::parse_osd_perf_json(JSONParser& parser, OSDPerfMetrics& metrics) {
  try {
    // Extract performance metrics from the JSON
    // The exact structure depends on the ceph osd perf output format
    uint64_t total_ops = 0;
    uint64_t total_latency_sum = 0;
    uint64_t total_queue_depth = 0;
    uint32_t osd_count = 0;

    // Look for OSD performance data
    JSONObj *perf_infos_obj = parser.find_obj("osd_perf_infos");
    if (perf_infos_obj && perf_infos_obj->is_array()) {
      auto perf_info_names = perf_infos_obj->get_array_elements();

      for (const auto& perf_info_name : perf_info_names) {
        JSONObj *perf_info = parser.find_obj(perf_info_name);
        if (perf_info) {
          // Extract metrics from each OSD
          JSONObj *perf_stats_obj = perf_info->find_obj("perf_stats");
          if (perf_stats_obj) {
            // Sum up the metrics
            JSONObj::data_val commit_lat_val;
            if (perf_stats_obj->get_data("commit_latency_ms", &commit_lat_val)) {
              double commit_lat = std::stod(commit_lat_val.str);
              total_latency_sum += static_cast<uint64_t>(commit_lat);
            }

            JSONObj::data_val apply_lat_val;
            if (perf_stats_obj->get_data("apply_latency_ms", &apply_lat_val)) {
              double apply_lat = std::stod(apply_lat_val.str);
              total_latency_sum += static_cast<uint64_t>(apply_lat);
            }

            osd_count++;
          }
        }
      }
    }

    // Calculate averages
    if (osd_count > 0) {
      metrics.op_latency_ms = total_latency_sum / (osd_count * 2); // commit + apply
      metrics.op_queue_depth = total_queue_depth / osd_count;
      metrics.op_throughput = total_ops;
    }

    // Set reasonable defaults for CPU and IO utilization
    // These would need to be queried separately from system metrics
    metrics.cpu_utilization = 30; // Placeholder
    metrics.io_utilization = 25;  // Placeholder

    return 0;
  } catch (const std::exception& e) {
    derr << __func__ << ": exception parsing JSON: " << e.what() << dendl;
    return -EIO;
  }
}

int OSDPerfMonitor::aggregate_osd_metrics(JSONParser& parser, OSDPerfMetrics& metrics) {
  // This is a fallback implementation that provides reasonable estimates
  // based on cluster state rather than real-time performance counters

  try {
    // Count active OSDs
    uint32_t active_osds = 0;
    JSONObj *osds_obj = parser.find_obj("osds");
    if (osds_obj && osds_obj->is_array()) {
      auto osd_names = osds_obj->get_array_elements();

      for (const auto& osd_name : osd_names) {
        JSONObj *osd = parser.find_obj(osd_name);
        if (osd) {
          JSONObj::data_val up_val, in_val;
          if (osd->get_data("up", &up_val) && osd->get_data("in", &in_val)) {
            int up = std::stoi(up_val.str);
            int in = std::stoi(in_val.str);
            if (up == 1 && in == 1) {
              active_osds++;
            }
          }
        }
      }
    }

    // Provide conservative estimates based on cluster size
    if (active_osds > 0) {
      // Estimate queue depth based on cluster size (larger clusters tend to have higher queue depths)
      metrics.op_queue_depth = std::min(100UL, 5UL + active_osds / 10);

      // Estimate latency (smaller clusters typically have lower latency)
      metrics.op_latency_ms = std::max(1UL, 50UL / std::max(1U, active_osds / 10));

      // Estimate throughput
      metrics.op_throughput = active_osds * 100; // 100 ops per OSD as baseline
    } else {
      // No active OSDs - system is likely overloaded
      metrics.op_queue_depth = 200;
      metrics.op_latency_ms = 100;
      metrics.op_throughput = 0;
    }

    // Conservative estimates for CPU and IO
    metrics.cpu_utilization = 40;
    metrics.io_utilization = 35;

    return 0;
  } catch (const std::exception& e) {
    derr << __func__ << ": exception aggregating metrics: " << e.what() << dendl;
    return -EIO;
  }
}

// TrimThrottleController implementation
TrimThrottleController::TrimThrottleController(CephContext* cct,
                                               std::shared_ptr<OSDPerfMonitor> monitor,
                                               const TrimThrottleConfig& config)
  : cct(cct), perf_monitor(monitor), config(config) {
  
  this->config.validate();
  current_concurrency = this->config.default_concurrency;
  current_batch_size = this->config.batch_size;
}

TrimThrottleController::~TrimThrottleController() {
  stop();
}

int TrimThrottleController::start() {
  if (running.exchange(true)) {
    return 0; // Already running
  }
  
  dout(10) << __func__ << ": starting trim throttle controller" << dendl;
  
  try {
    control_thread = std::make_unique<std::thread>(&TrimThrottleController::control_loop, this);
  } catch (const std::exception& e) {
    running = false;
    derr << __func__ << ": failed to start control thread: " << e.what() << dendl;
    return -ENOMEM;
  }
  
  return 0;
}

void TrimThrottleController::stop() {
  if (!running.exchange(false)) {
    return; // Already stopped
  }
  
  dout(10) << __func__ << ": stopping trim throttle controller" << dendl;
  
  if (control_thread && control_thread->joinable()) {
    control_thread->join();
  }
  control_thread.reset();
}

uint32_t TrimThrottleController::get_concurrency_limit() const {
  return current_concurrency;
}

uint32_t TrimThrottleController::get_batch_size() const {
  return current_batch_size;
}

std::chrono::milliseconds TrimThrottleController::get_batch_delay() const {
  std::lock_guard lock(config_lock);
  
  if (paused || (perf_monitor && perf_monitor->is_overloaded())) {
    return config.adaptive_delay;
  }
  
  return config.batch_delay;
}

bool TrimThrottleController::should_pause() const {
  return paused;
}

void TrimThrottleController::update_config(const TrimThrottleConfig& new_config) {
  std::lock_guard lock(config_lock);
  config = new_config;
  config.validate();
  
  dout(10) << __func__ << ": configuration updated" << dendl;
}

TrimThrottleConfig TrimThrottleController::get_config() const {
  std::lock_guard lock(config_lock);
  return config;
}

void TrimThrottleController::control_loop() {
  dout(10) << __func__ << ": control loop started" << dendl;
  
  while (running) {
    if (perf_monitor) {
      OSDPerfMetrics metrics = perf_monitor->get_metrics();
      adjust_parameters(metrics);
    }
    
    // Sleep for control interval
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
  }
  
  dout(10) << __func__ << ": control loop stopped" << dendl;
}

void TrimThrottleController::adjust_parameters(const OSDPerfMetrics& metrics) {
  double load_factor = metrics.load_factor();
  
  std::lock_guard lock(config_lock);
  
  // Adjust concurrency based on load
  uint32_t new_concurrency = calculate_optimal_concurrency(load_factor);
  if (new_concurrency != current_concurrency) {
    dout(15) << __func__ << ": adjusting concurrency from " 
             << current_concurrency << " to " << new_concurrency
             << " (load_factor=" << load_factor << ")" << dendl;
    current_concurrency = new_concurrency;
  }
  
  // Adjust batch size based on load
  uint32_t new_batch_size = calculate_optimal_batch_size(load_factor);
  if (new_batch_size != current_batch_size) {
    dout(15) << __func__ << ": adjusting batch size from "
             << current_batch_size << " to " << new_batch_size
             << " (load_factor=" << load_factor << ")" << dendl;
    current_batch_size = new_batch_size;
  }
  
  // Determine if we should pause
  bool should_pause_now = load_factor > config.critical_threshold;
  if (should_pause_now != paused) {
    dout(10) << __func__ << ": " << (should_pause_now ? "pausing" : "resuming")
             << " trim operations (load_factor=" << load_factor << ")" << dendl;
    paused = should_pause_now;
  }
}

uint32_t TrimThrottleController::calculate_optimal_concurrency(double load_factor) const {
  if (load_factor < config.load_threshold) {
    // Low load - use maximum concurrency
    return config.max_concurrency;
  } else if (load_factor > config.critical_threshold) {
    // Critical load - use minimum concurrency
    return config.min_concurrency;
  } else {
    // Scale linearly between thresholds
    double scale = (config.critical_threshold - load_factor) / 
                   (config.critical_threshold - config.load_threshold);
    uint32_t range = config.max_concurrency - config.min_concurrency;
    return config.min_concurrency + static_cast<uint32_t>(range * scale);
  }
}

uint32_t TrimThrottleController::calculate_optimal_batch_size(double load_factor) const {
  if (load_factor < config.load_threshold) {
    // Low load - use maximum batch size
    return config.max_batch_size;
  } else if (load_factor > config.critical_threshold) {
    // Critical load - use minimum batch size
    return config.min_batch_size;
  } else {
    // Scale linearly between thresholds
    double scale = (config.critical_threshold - load_factor) / 
                   (config.critical_threshold - config.load_threshold);
    uint32_t range = config.max_batch_size - config.min_batch_size;
    return config.min_batch_size + static_cast<uint32_t>(range * scale);
  }
}

// BatchProcessor implementation
template<typename ItemType>
BatchProcessor<ItemType>::BatchProcessor(CephContext* cct,
                                         std::shared_ptr<TrimThrottleController> controller)
  : cct(cct), throttle_controller(controller) {
}

template<typename ItemType>
int BatchProcessor<ItemType>::process_items(const std::vector<ItemType>& items,
                                            ProcessBatchFunc process_func,
                                            const DoutPrefixProvider* dpp) {
  if (items.empty()) {
    return 0;
  }

  uint32_t batch_size = throttle_controller->get_batch_size();
  return process_items_with_size(items, batch_size, process_func, dpp);
}

template<typename ItemType>
int BatchProcessor<ItemType>::process_items_with_size(const std::vector<ItemType>& items,
                                                      uint32_t batch_size,
                                                      ProcessBatchFunc process_func,
                                                      const DoutPrefixProvider* dpp) {
  if (items.empty()) {
    return 0;
  }

  ldpp_dout(dpp, 10) << __func__ << ": processing " << items.size()
                     << " items in batches of " << batch_size << dendl;

  int total_processed = 0;
  int last_error = 0;

  for (size_t i = 0; i < items.size(); i += batch_size) {
    // Check if we should pause
    if (throttle_controller->should_pause()) {
      ldpp_dout(dpp, 10) << __func__ << ": pausing due to system overload" << dendl;

      // Wait for system to recover
      auto delay = throttle_controller->get_batch_delay();
      std::this_thread::sleep_for(delay);
      continue;
    }

    // Create batch
    size_t end_idx = std::min(i + batch_size, items.size());
    std::vector<ItemType> batch(items.begin() + i, items.begin() + end_idx);

    // Process batch
    int r = process_single_batch(batch, process_func, dpp);
    if (r < 0) {
      ldpp_dout(dpp, 5) << __func__ << ": batch processing failed: "
                        << cpp_strerror(r) << dendl;
      last_error = r;
      // Continue with next batch instead of failing completely
    } else {
      total_processed += batch.size();
    }

    // Add delay between batches to avoid overwhelming the system
    if (i + batch_size < items.size()) {
      auto delay = throttle_controller->get_batch_delay();
      std::this_thread::sleep_for(delay);
    }
  }

  ldpp_dout(dpp, 10) << __func__ << ": processed " << total_processed
                     << " out of " << items.size() << " items" << dendl;

  return last_error;
}

template<typename ItemType>
int BatchProcessor<ItemType>::process_single_batch(const std::vector<ItemType>& batch,
                                                   ProcessBatchFunc process_func,
                                                   const DoutPrefixProvider* dpp) {
  if (batch.empty()) {
    return 0;
  }

  ldpp_dout(dpp, 20) << __func__ << ": processing batch of " << batch.size() << " items" << dendl;

  try {
    return process_func(batch);
  } catch (const std::exception& e) {
    ldpp_dout(dpp, 1) << __func__ << ": exception during batch processing: " << e.what() << dendl;
    return -EIO;
  }
}

// TrimThrottleManager implementation
TrimThrottleManager::TrimThrottleManager(CephContext* cct, rgw::sal::RadosStore* store)
  : cct(cct), store(store) {
}

TrimThrottleManager::~TrimThrottleManager() {
  shutdown();
}

int TrimThrottleManager::init(const TrimThrottleConfig& config) {
  if (initialized.exchange(true)) {
    return 0; // Already initialized
  }

  dout(10) << __func__ << ": initializing trim throttle manager" << dendl;

  // Create performance monitor
  perf_monitor = std::make_shared<OSDPerfMonitor>(cct, store);
  int r = perf_monitor->start();
  if (r < 0) {
    derr << __func__ << ": failed to start performance monitor: " << cpp_strerror(r) << dendl;
    initialized = false;
    return r;
  }

  // Create throttle controller
  controller = std::make_shared<TrimThrottleController>(cct, perf_monitor, config);
  r = controller->start();
  if (r < 0) {
    derr << __func__ << ": failed to start throttle controller: " << cpp_strerror(r) << dendl;
    perf_monitor->stop();
    initialized = false;
    return r;
  }

  dout(10) << __func__ << ": trim throttle manager initialized successfully" << dendl;
  return 0;
}

void TrimThrottleManager::shutdown() {
  if (!initialized.exchange(false)) {
    return; // Already shutdown
  }

  dout(10) << __func__ << ": shutting down trim throttle manager" << dendl;

  if (controller) {
    controller->stop();
    controller.reset();
  }

  if (perf_monitor) {
    perf_monitor->stop();
    perf_monitor.reset();
  }

  dout(10) << __func__ << ": trim throttle manager shutdown complete" << dendl;
}

bool TrimThrottleManager::is_overloaded() const {
  return perf_monitor ? perf_monitor->is_overloaded() : false;
}

uint32_t TrimThrottleManager::get_concurrency_limit() const {
  return controller ? controller->get_concurrency_limit() : 1;
}

std::chrono::milliseconds TrimThrottleManager::get_batch_delay() const {
  return controller ? controller->get_batch_delay() : std::chrono::milliseconds(100);
}

// TrimThrottleConfigHelper implementation
TrimThrottleConfig TrimThrottleConfigHelper::load_from_ceph_context(CephContext* cct) {
  TrimThrottleConfig config;

  // Load performance thresholds
  config.max_queue_depth = 100; // Fixed for now, could be configurable
  config.max_latency_ms = 50;   // Fixed for now, could be configurable
  config.max_cpu_util = 80;     // Fixed for now, could be configurable
  config.max_io_util = 80;      // Fixed for now, could be configurable

  // Load throttling parameters
  config.min_concurrency = cct->_conf.get_val<int64_t>("rgw_trim_throttle_min_concurrency");
  config.max_concurrency = cct->_conf.get_val<int64_t>("rgw_trim_throttle_max_concurrency");
  config.default_concurrency = std::clamp(
    static_cast<uint32_t>((config.min_concurrency + config.max_concurrency) / 2),
    config.min_concurrency, config.max_concurrency);

  // Load batch processing parameters
  config.batch_size = cct->_conf.get_val<int64_t>("rgw_trim_throttle_batch_size");
  config.min_batch_size = 10;   // Fixed minimum
  config.max_batch_size = 1000; // Fixed maximum

  // Load timing parameters
  int monitor_interval = cct->_conf.get_val<int64_t>("rgw_trim_throttle_monitor_interval");
  config.monitor_interval = std::chrono::milliseconds(monitor_interval * 1000);
  config.batch_delay = std::chrono::milliseconds(100);        // Fixed for now
  config.adaptive_delay = std::chrono::milliseconds(1000);    // Fixed for now

  // Load load balancing parameters
  config.load_threshold = cct->_conf.get_val<double>("rgw_trim_throttle_load_threshold");
  config.critical_threshold = cct->_conf.get_val<double>("rgw_trim_throttle_critical_threshold");

  validate_config(config);
  return config;
}

void TrimThrottleConfigHelper::update_from_ceph_context(TrimThrottleConfig& config, CephContext* cct) {
  // Update parameters that can be changed at runtime
  config.min_concurrency = cct->_conf.get_val<int64_t>("rgw_trim_throttle_min_concurrency");
  config.max_concurrency = cct->_conf.get_val<int64_t>("rgw_trim_throttle_max_concurrency");
  config.batch_size = cct->_conf.get_val<int64_t>("rgw_trim_throttle_batch_size");
  config.load_threshold = cct->_conf.get_val<double>("rgw_trim_throttle_load_threshold");
  config.critical_threshold = cct->_conf.get_val<double>("rgw_trim_throttle_critical_threshold");

  int monitor_interval = cct->_conf.get_val<int64_t>("rgw_trim_throttle_monitor_interval");
  config.monitor_interval = std::chrono::milliseconds(monitor_interval * 1000);

  validate_config(config);
}

void TrimThrottleConfigHelper::validate_config(TrimThrottleConfig& config) {
  // Ensure concurrency limits are sane
  config.min_concurrency = std::max(1U, config.min_concurrency);
  config.max_concurrency = std::max(config.min_concurrency, config.max_concurrency);
  config.default_concurrency = std::clamp(config.default_concurrency,
                                          config.min_concurrency, config.max_concurrency);

  // Ensure batch sizes are sane
  config.min_batch_size = std::max(1U, config.min_batch_size);
  config.max_batch_size = std::max(config.min_batch_size, config.max_batch_size);
  config.batch_size = std::clamp(config.batch_size, config.min_batch_size, config.max_batch_size);

  // Ensure thresholds are in valid range
  config.load_threshold = std::clamp(config.load_threshold, 0.1, 1.0);
  config.critical_threshold = std::clamp(config.critical_threshold,
                                        config.load_threshold, 1.0);

  // Ensure timing parameters are reasonable
  if (config.monitor_interval < std::chrono::milliseconds(1000)) {
    config.monitor_interval = std::chrono::milliseconds(1000);
  }
  if (config.monitor_interval > std::chrono::milliseconds(60000)) {
    config.monitor_interval = std::chrono::milliseconds(60000);
  }
}

// Explicit template instantiations for common types
template class BatchProcessor<std::string>;
template class BatchProcessor<rgw_obj_key>;

} // namespace rgw
